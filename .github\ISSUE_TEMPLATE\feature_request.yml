name: Feature request
description: Propose a feature request for RAGFlow.
title: "[Feature Request]: "
labels: [feature request]
body:
  - type: checkboxes
    attributes:
      label: Is there an existing issue for the same feature request?
      description: Please check if an issue already exists for the feature you request.
      options:
        - label: I have checked the existing issues.
          required: true
  - type: textarea
    attributes:
      label: Is your feature request related to a problem?
      description: |
        A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]
      render: Markdown
    validations:
      required: false
  - type: textarea
    attributes:
      label: Describe the feature you'd like
      description: A clear and concise description of what you want to happen.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Describe implementation you've considered
      description: A clear and concise description of implementation you've considered or investigated.
    validations:
      required: false
  - type: textarea
    attributes:
      label: Documentation, adoption, use case
      description: If you can, explain some scenarios how users might use this, situations it would be helpful in. Any API designs, mockups, or diagrams are also helpful.
      render: Markdown
    validations:
      required: false
  - type: textarea
    attributes:
      label: Additional information
      description: |
        Add any other context or screenshots about the feature request here.
    validations:
      required: false