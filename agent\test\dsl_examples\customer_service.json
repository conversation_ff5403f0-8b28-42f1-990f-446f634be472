{"components": {"begin": {"obj": {"component_name": "<PERSON><PERSON>", "params": {"prologue": "Hi! How can I help you?"}}, "downstream": ["answer:0"], "upstream": []}, "answer:0": {"obj": {"component_name": "Answer", "params": {}}, "downstream": ["categorize:0"], "upstream": ["begin", "generate:0", "generate:casual", "generate:answer", "generate:complain", "generate:ask_contact", "message:get_contact"]}, "categorize:0": {"obj": {"component_name": "Categorize", "params": {"llm_id": "deepseek-chat", "category_description": {"product_related": {"description": "The question is about the product usage, appearance and how it works.", "examples": "Why it always beaming?\nHow to install it onto the wall?\nIt leaks, what to do?\nException: Can't connect to ES cluster\nHow to build the RAGFlow image from scratch", "to": "retrieval:0"}, "casual": {"description": "The question is not about the product usage, appearance and how it works. Just casual chat.", "examples": "How are you doing?\nWhat is your name?\nAre you a robot?\nWhat's the weather?\nWill it rain?", "to": "generate:casual"}, "complain": {"description": "<PERSON><PERSON><PERSON> even curse about the product or service you provide. But the comment is not specific enough.", "examples": "How bad is it.\nIt's really sucks.\nDamn, for God's sake, can it be more steady?\nShit, I just can't use this shit.\nI can't stand it anymore.", "to": "generate:complain"}, "answer": {"description": "This answer provide a specific contact information, like e-mail, phone number, wechat number, line number, twitter, discord, etc,.", "examples": "My phone number is 203921\nkevin<PERSON>.<EMAIL>\nThis is my discord number: johndo<PERSON>_29384", "to": "message:get_contact"}}, "message_history_window_size": 8}}, "downstream": ["retrieval:0", "generate:casual", "generate:complain", "message:get_contact"], "upstream": ["answer:0"]}, "generate:casual": {"obj": {"component_name": "Generate", "params": {"llm_id": "deepseek-chat", "prompt": "You are a customer support. But the customer wants to have a casual chat with you instead of consulting about the product. Be nice, funny, enthusiasm and concern.", "temperature": 0.9, "message_history_window_size": 12, "cite": false}}, "downstream": ["answer:0"], "upstream": ["categorize:0"]}, "generate:complain": {"obj": {"component_name": "Generate", "params": {"llm_id": "deepseek-chat", "prompt": "You are a customer support. the Customers complain even curse about the products but not specific enough. You need to ask him/her what's the specific problem with the product. Be nice, patient and concern to soothe your customers’ emotions at first place.", "temperature": 0.9, "message_history_window_size": 12, "cite": false}}, "downstream": ["answer:0"], "upstream": ["categorize:0"]}, "retrieval:0": {"obj": {"component_name": "Retrieval", "params": {"similarity_threshold": 0.2, "keywords_similarity_weight": 0.3, "top_n": 6, "top_k": 1024, "rerank_id": "BAAI/bge-reranker-v2-m3", "kb_ids": ["869a236818b811ef91dffa163e197198"]}}, "downstream": ["relevant:0"], "upstream": ["categorize:0"]}, "relevant:0": {"obj": {"component_name": "Relevant", "params": {"llm_id": "deepseek-chat", "temperature": 0.02, "yes": "generate:answer", "no": "generate:ask_contact"}}, "downstream": ["generate:answer", "generate:ask_contact"], "upstream": ["retrieval:0"]}, "generate:answer": {"obj": {"component_name": "Generate", "params": {"llm_id": "deepseek-chat", "prompt": "You are an intelligent assistant. Please answer the question based on content of knowledge base. When all knowledge base content is irrelevant to the question, your answer must include the sentence \"The answer you are looking for is not found in the knowledge base!\". Answers need to consider chat history.\n      Knowledge base content is as following:\n      {input}\n      The above is the content of knowledge base.", "temperature": 0.02}}, "downstream": ["answer:0"], "upstream": ["relevant:0"]}, "generate:ask_contact": {"obj": {"component_name": "Generate", "params": {"llm_id": "deepseek-chat", "prompt": "You are a customer support. But you can't answer to customers' question. You need to request their contact like E-mail, phone number, Wechat number, LINE number, twitter, discord, etc,. Product experts will contact them later. Please do not ask the same question twice.", "temperature": 0.9, "message_history_window_size": 12, "cite": false}}, "downstream": ["answer:0"], "upstream": ["relevant:0"]}, "message:get_contact": {"obj": {"component_name": "Message", "params": {"messages": ["Okay, I've already write this down. What else I can do for you?", "Get it. What else I can do for you?", "Thanks for your trust! Our expert will contact ASAP. So, anything else I can do for you?", "Thanks! So, anything else I can do for you?"]}}, "downstream": ["answer:0"], "upstream": ["categorize:0"]}}, "history": [], "messages": [], "path": [], "reference": [], "answer": []}