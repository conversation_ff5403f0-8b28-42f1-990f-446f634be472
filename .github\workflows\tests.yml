name: tests

on:
  push:
    branches:
      - 'main'
      - '*.*.*'
    paths-ignore:
      - 'docs/**'
      - '*.md'
      - '*.mdx'
  pull_request:
    types: [ opened, synchronize, reopened, labeled ]
    paths-ignore:
      - 'docs/**'
      - '*.md'
      - '*.mdx'

# https://docs.github.com/en/actions/using-jobs/using-concurrency
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  ragflow_tests:
    name: ragflow_tests
    # https://docs.github.com/en/actions/using-jobs/using-conditions-to-control-job-execution
    # https://github.com/orgs/community/discussions/26261
    if: ${{ github.event_name != 'pull_request' || contains(github.event.pull_request.labels.*.name, 'ci') }}
    runs-on: [ "self-hosted", "debug" ]
    steps:
      # https://github.com/hmarr/debug-action
      #- uses: hmarr/debug-action@v2

      - name: Show who triggered this workflow
        run: |
          echo "Workflow triggered by ${{ github.event_name }}"

      - name: Ensure workspace ownership
        run: echo "chown -R $USER $GITHUB_WORKSPACE" && sudo chown -R $USER $GITHUB_WORKSPACE

      # https://github.com/actions/checkout/issues/1781
      - name: Check out code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true

      # https://github.com/astral-sh/ruff-action
      - name: Static check with Ruff
        uses: astral-sh/ruff-action@v2
        with:
          version: ">=0.8.2"
          args: "check --ignore E402"

      - name: Build ragflow:nightly-slim
        run: |
          RUNNER_WORKSPACE_PREFIX=${RUNNER_WORKSPACE_PREFIX:-$HOME}
          sudo docker pull ubuntu:22.04
          sudo docker build --progress=plain --build-arg LIGHTEN=1 --build-arg NEED_MIRROR=1 -f Dockerfile -t infiniflow/ragflow:nightly-slim .

      - name: Build ragflow:nightly
        run: |
          sudo docker build --progress=plain --build-arg NEED_MIRROR=1 -f Dockerfile -t infiniflow/ragflow:nightly .

      - name: Start ragflow:nightly-slim
        run: |
          echo "RAGFLOW_IMAGE=infiniflow/ragflow:nightly-slim" >> docker/.env
          sudo docker compose -f docker/docker-compose.yml up -d

      - name: Stop ragflow:nightly-slim
        if: always()  # always run this step even if previous steps failed
        run: |
          sudo docker compose -f docker/docker-compose.yml down -v

      - name: Start ragflow:nightly
        run: |
          echo "RAGFLOW_IMAGE=infiniflow/ragflow:nightly" >> docker/.env
          sudo docker compose -f docker/docker-compose.yml up -d

      - name: Run sdk tests against Elasticsearch
        run: |
          export http_proxy=""; export https_proxy=""; export no_proxy=""; export HTTP_PROXY=""; export HTTPS_PROXY=""; export NO_PROXY=""
          export HOST_ADDRESS=http://host.docker.internal:9380
          until sudo docker exec ragflow-server curl -s --connect-timeout 5 ${HOST_ADDRESS} > /dev/null; do
            echo "Waiting for service to be available..."
            sleep 5
          done
          cd sdk/python && uv sync --python 3.10 --frozen && uv pip install . && source .venv/bin/activate && cd test/test_sdk_api && pytest -s --tb=short get_email.py t_dataset.py t_chat.py t_session.py t_document.py t_chunk.py

      - name: Run frontend api tests against Elasticsearch
        run: |
          export http_proxy=""; export https_proxy=""; export no_proxy=""; export HTTP_PROXY=""; export HTTPS_PROXY=""; export NO_PROXY=""
          export HOST_ADDRESS=http://host.docker.internal:9380
          until sudo docker exec ragflow-server curl -s --connect-timeout 5 ${HOST_ADDRESS} > /dev/null; do
            echo "Waiting for service to be available..."
            sleep 5
          done
          cd sdk/python && uv sync --python 3.10 --frozen && uv pip install . && source .venv/bin/activate && cd test/test_frontend_api && pytest -s --tb=short get_email.py test_dataset.py 
          

      - name: Stop ragflow:nightly
        if: always()  # always run this step even if previous steps failed
        run: |
          sudo docker compose -f docker/docker-compose.yml down -v

      - name: Start ragflow:nightly
        run: |
          sudo DOC_ENGINE=infinity docker compose -f docker/docker-compose.yml up -d

      - name: Run sdk tests against Infinity
        run: |
          export http_proxy=""; export https_proxy=""; export no_proxy=""; export HTTP_PROXY=""; export HTTPS_PROXY=""; export NO_PROXY=""
          export HOST_ADDRESS=http://host.docker.internal:9380
          until sudo docker exec ragflow-server curl -s --connect-timeout 5 ${HOST_ADDRESS} > /dev/null; do
            echo "Waiting for service to be available..."
            sleep 5
          done
          cd sdk/python && uv sync --python 3.10 --frozen && uv pip install . && source .venv/bin/activate && cd test/test_sdk_api && pytest -s --tb=short get_email.py t_dataset.py t_chat.py t_session.py t_document.py t_chunk.py

      - name: Run frontend api tests against Infinity
        run: |
          export http_proxy=""; export https_proxy=""; export no_proxy=""; export HTTP_PROXY=""; export HTTPS_PROXY=""; export NO_PROXY=""
          export HOST_ADDRESS=http://host.docker.internal:9380
          until sudo docker exec ragflow-server curl -s --connect-timeout 5 ${HOST_ADDRESS} > /dev/null; do
            echo "Waiting for service to be available..."
            sleep 5
          done
          cd sdk/python && uv sync --python 3.10 --frozen && uv pip install . && source .venv/bin/activate && cd test/test_frontend_api && pytest -s --tb=short get_email.py test_dataset.py

      - name: Stop ragflow:nightly
        if: always()  # always run this step even if previous steps failed
        run: |
          sudo DOC_ENGINE=infinity docker compose -f docker/docker-compose.yml down -v
