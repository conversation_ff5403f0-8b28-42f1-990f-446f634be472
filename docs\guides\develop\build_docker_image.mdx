---
sidebar_position: 1
slug: /build_docker_image
---

# Build a RAGFlow Docker Image
import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

A guide explaining how to build a RAGFlow Docker image from its source code. By following this guide, you'll be able to create a local Docker image that can be used for development, debugging, or testing purposes.

## Target Audience

- Developers who have added new features or modified the existing code and require a Docker image to view and debug their changes.
- Developers seeking to build a RAGFlow Docker image for an ARM64 platform.
- Testers aiming to explore the latest features of RAGFlow in a Docker image.

## Prerequisites

- CPU &ge; 4 cores
- RAM &ge; 16 GB
- Disk &ge; 50 GB
- Docker &ge; 24.0.0 & Docker Compose &ge; v2.26.1
- For ARM64 platforms, please upgrade the `xgboost` version in **pyproject.toml** to `1.6.0` and ensure **unixODBC** is properly installed.

## Build a Docker image

<Tabs
  defaultValue="without"
  values={[
    {label: 'Build a Docker image without embedding models', value: 'without'},
    {label: 'Build a Docker image including embedding models', value: 'including'}
  ]}>
  <TabItem value="without">

This image is approximately 2 GB in size and relies on external LLM and embedding services.

:::tip NOTE
While we also test RAGFlow on ARM64 platforms, we do not maintain RAGFlow Docker images for ARM. However, you can build an image yourself on a `linux/arm64` or `darwin/arm64` host machine as well.
:::

```bash
git clone https://github.com/infiniflow/ragflow.git
cd ragflow/
docker build --build-arg LIGHTEN=1 -f Dockerfile -t infiniflow/ragflow:nightly-slim .
```


  </TabItem>
  <TabItem value="including">

This image is approximately 9 GB in size. As it includes embedding models, it relies on external LLM services only.  

:::tip NOTE
While we also test RAGFlow on ARM64 platforms, we do not maintain RAGFlow Docker images for ARM. However, you can build an image yourself on a `linux/arm64` or `darwin/arm64` host machine.
:::

```bash
git clone https://github.com/infiniflow/ragflow.git
cd ragflow/
uv run download_deps.py
docker build -f Dockerfile.deps -t infiniflow/ragflow_deps .
docker build -f Dockerfile -t infiniflow/ragflow:nightly .
docker build --build-arg LIGHTEN=1 -f Dockerfile -t infiniflow/ragflow:nightly-slim .
```

  </TabItem>
</Tabs>
