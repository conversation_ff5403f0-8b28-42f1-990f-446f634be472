{"components": {"begin": {"obj": {"component_name": "<PERSON><PERSON>", "params": {"prologue": "Hi there!"}}, "downstream": ["answer:0"], "upstream": []}, "answer:0": {"obj": {"component_name": "Answer", "params": {}}, "downstream": ["retrieval:0"], "upstream": ["begin", "generate:0"]}, "retrieval:0": {"obj": {"component_name": "Retrieval", "params": {"similarity_threshold": 0.2, "keywords_similarity_weight": 0.3, "top_n": 6, "top_k": 1024, "rerank_id": "BAAI/bge-reranker-v2-m3", "kb_ids": ["869a236818b811ef91dffa163e197198"]}}, "downstream": ["generate:0"], "upstream": ["answer:0"]}, "generate:0": {"obj": {"component_name": "Generate", "params": {"llm_id": "deepseek-chat", "prompt": "You are an intelligent assistant. Please summarize the content of the knowledge base to answer the question. Please list the data in the knowledge base and answer in detail. When all knowledge base content is irrelevant to the question, your answer must include the sentence \"The answer you are looking for is not found in the knowledge base!\" Answers need to consider chat history.\n      Here is the knowledge base:\n      {input}\n      The above is the knowledge base.", "temperature": 0.2}}, "downstream": ["answer:0"], "upstream": ["retrieval:0"]}}, "history": [], "messages": [], "reference": {}, "path": [], "answer": []}