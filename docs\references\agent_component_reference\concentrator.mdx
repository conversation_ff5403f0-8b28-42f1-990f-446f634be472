---
sidebar_position: 10
slug: /concentrator_component
---

# Concentrator component

A component that directs execution flow to multiple downstream components.

---

The **Concentrator** component acts as a "repeater" of execution flow, transmitting a flow to multiple downstream components.


## Scenarios

A **Concentrator** component enhances the current UX design. For a component originally designed to support only one downstream component, you can append a **Concentrator**, enabling it to have multiple downstream components.

## Examples

Explore our general-purpose chatbot agent template, featuring a **Concentrator** component (component ID: **medical**) that relays an execution flow from category 2 of the **Categorize** component to two translator components:

1. Click the **Agent** tab at the top center of the page to access the **Agent** page.
2. Click **+ Create agent** on the top right of the page to open the **agent template** page.
3. On the **agent template** page, hover over the **General-purpose chatbot** card and click **Use this template**.
4. Name your new agent and click **OK** to enter the workflow editor.